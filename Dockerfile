FROM node:20-alpine3.20

ARG PROJECT
ENV PROJECT_NAME=$PROJECT \
    PNPM_HOME=/root/.pnpm-store \
    NPM_CONFIG_REGISTRY=https://registry.npmmirror.com

WORKDIR /app

RUN corepack enable && corepack prepare pnpm@8.6.6 --activate

# 复制 workspace 配置和根目录的包管理文件
COPY pnpm-workspace.yaml package.json pnpm-lock.yaml ./

# 创建目录结构并复制所有 package.json 文件
# 注意：需要保持目录结构，所以分别复制每个子项目的 package.json
COPY apps/dispatcher/package.json ./apps/dispatcher/
COPY apps/moer_overseas/package.json ./apps/moer_overseas/
COPY apps/yuhe/package.json ./apps/yuhe/
COPY packages/config/package.json ./packages/config/
COPY packages/lib/package.json ./packages/lib/
COPY packages/model/package.json ./packages/model/
COPY packages/service/package.json ./packages/service/

# 安装依赖（这一层会被缓存，除非 package.json 或 pnpm-lock.yaml 发生变化）
# 使用 --frozen-lockfile 确保使用精确的锁文件版本
RUN pnpm install --frozen-lockfile

# 复制源代码（这一层在代码变更时会重新构建，但不会重新安装依赖）
COPY . .

# 生成 Prisma 客户端
RUN pnpm run prisma_generate

# 根据项目设置工作目录并启动
CMD cd apps/$PROJECT_NAME && pnpm run client